'use client';

import { useState } from 'react';
import { cn, isValidYouTubeUrl } from '@/lib/utils';
import { LoadingSpinner } from './LoadingSpinner';
import { InlineError } from './ErrorDisplay';
import { PlayIcon, LinkIcon } from '@heroicons/react/24/outline';

interface PlaylistFormProps {
  onSubmit: (url: string, defaultFormat: 'mp4' | 'mp3', defaultQuality: string) => void;
  isLoading?: boolean;
  disabled?: boolean;
  className?: string;
}

/**
 * Playlist URL input form component
 */
export function PlaylistForm({
  onSubmit,
  isLoading = false,
  disabled = false,
  className
}: PlaylistFormProps) {
  const [url, setUrl] = useState('');
  const [error, setError] = useState('');
  const [touched, setTouched] = useState(false);
  const [defaultFormat, setDefaultFormat] = useState<'mp4' | 'mp3'>('mp4');
  const [defaultQuality, setDefaultQuality] = useState('720p');

  const validateUrl = (value: string): string => {
    if (!value.trim()) {
      return 'Please enter a YouTube playlist URL';
    }

    if (!isValidYouTubeUrl(value)) {
      return 'Please enter a valid YouTube URL';
    }

    // Check if it's likely a playlist URL
    if (!value.includes('list=') && !value.includes('playlist')) {
      return 'Please enter a playlist URL (should contain "list=" parameter)';
    }

    return '';
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setTouched(true);

    const validationError = validateUrl(url);
    if (validationError) {
      setError(validationError);
      return;
    }

    setError('');
    onSubmit(url.trim(), defaultFormat, defaultQuality);
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setUrl(value);
    
    if (touched) {
      const validationError = validateUrl(value);
      setError(validationError);
    }
  };

  const handleBlur = () => {
    setTouched(true);
    const validationError = validateUrl(url);
    setError(validationError);
  };

  const isFormDisabled = disabled || isLoading;
  const hasError = touched && !!error;

  return (
    <div className={cn('w-full max-w-2xl', className)}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label
            htmlFor="playlist-url"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            YouTube Playlist URL
          </label>
          
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <LinkIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </div>
            
            <input
              id="playlist-url"
              type="url"
              value={url}
              onChange={handleUrlChange}
              onBlur={handleBlur}
              disabled={isFormDisabled}
              placeholder="https://www.youtube.com/playlist?list=..."
              className={cn(
                'block w-full pl-10 pr-3 py-3 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
                hasError
                  ? 'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500'
                  : 'border-gray-300 text-gray-900',
                isFormDisabled && 'bg-gray-50 cursor-not-allowed'
              )}
              aria-invalid={hasError ? 'true' : 'false'}
              aria-describedby={hasError ? 'url-error' : undefined}
            />
          </div>
          
          {hasError && (
            <div id="url-error">
              <InlineError
                message={error}
                className="mt-2"
              />
            </div>
          )}
          
          <p className="mt-2 text-sm text-gray-500">
            Enter a YouTube playlist URL to download all videos in the playlist.
          </p>
        </div>

        {/* Default Format and Quality Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label
              htmlFor="default-format"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Default Format
            </label>
            <select
              id="default-format"
              value={defaultFormat}
              onChange={(e) => {
                const format = e.target.value as 'mp4' | 'mp3';
                setDefaultFormat(format);
                setDefaultQuality(format === 'mp4' ? '720p' : '192kbps');
              }}
              disabled={isFormDisabled}
              className={cn(
                'block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
                'border-gray-300 text-gray-900',
                isFormDisabled && 'bg-gray-50 cursor-not-allowed'
              )}
            >
              <option value="mp4">MP4 (Video)</option>
              <option value="mp3">MP3 (Audio Only)</option>
            </select>
          </div>

          <div>
            <label
              htmlFor="default-quality"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Default Quality
            </label>
            <select
              id="default-quality"
              value={defaultQuality}
              onChange={(e) => setDefaultQuality(e.target.value)}
              disabled={isFormDisabled}
              className={cn(
                'block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
                'border-gray-300 text-gray-900',
                isFormDisabled && 'bg-gray-50 cursor-not-allowed'
              )}
            >
              {defaultFormat === 'mp4' ? (
                <>
                  <option value="1080p">1080p</option>
                  <option value="720p">720p</option>
                  <option value="480p">480p</option>
                  <option value="360p">360p</option>
                  <option value="240p">240p</option>
                  <option value="144p">144p</option>
                </>
              ) : (
                <>
                  <option value="320kbps">320 kbps</option>
                  <option value="256kbps">256 kbps</option>
                  <option value="192kbps">192 kbps</option>
                  <option value="128kbps">128 kbps</option>
                  <option value="96kbps">96 kbps</option>
                  <option value="64kbps">64 kbps</option>
                </>
              )}
            </select>
          </div>
        </div>

        <button
          type="submit"
          disabled={isFormDisabled || hasError}
          className={cn(
            'w-full flex items-center justify-center gap-2 px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
            isFormDisabled || hasError
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800'
          )}
          aria-label={isLoading ? 'Loading playlist...' : 'Download all videos'}
        >
          {isLoading ? (
            <>
              <LoadingSpinner size="sm" />
              <span>Loading Playlist...</span>
            </>
          ) : (
            <>
              <PlayIcon className="h-5 w-5" aria-hidden="true" />
              <span>Download All Videos</span>
            </>
          )}
        </button>
      </form>

      {/* Example URLs */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-2">
          Example playlist URLs:
        </h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• https://www.youtube.com/playlist?list=PLxxxxxx</li>
          <li>• https://youtube.com/watch?v=xxxxx&list=PLxxxxxx</li>
          <li>• https://youtu.be/xxxxx?list=PLxxxxxx</li>
        </ul>
      </div>
    </div>
  );
}
