'use client';

import { useState } from 'react';
import { cn, isValidYouTubeUrl } from '@/lib/utils';
import { LoadingSpinner } from './LoadingSpinner';
import { InlineError } from './ErrorDisplay';
import { PlayIcon, LinkIcon } from '@heroicons/react/24/outline';

interface PlaylistFormProps {
  onSubmit: (url: string) => void;
  isLoading?: boolean;
  disabled?: boolean;
  className?: string;
}

/**
 * Playlist URL input form component
 */
export function PlaylistForm({
  onSubmit,
  isLoading = false,
  disabled = false,
  className
}: PlaylistFormProps) {
  const [url, setUrl] = useState('');
  const [error, setError] = useState('');
  const [touched, setTouched] = useState(false);

  const validateUrl = (value: string): string => {
    if (!value.trim()) {
      return 'Please enter a YouTube playlist URL';
    }

    if (!isValidYouTubeUrl(value)) {
      return 'Please enter a valid YouTube URL';
    }

    // Check if it's likely a playlist URL
    if (!value.includes('list=') && !value.includes('playlist')) {
      return 'Please enter a playlist URL (should contain "list=" parameter)';
    }

    return '';
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setTouched(true);

    const validationError = validateUrl(url);
    if (validationError) {
      setError(validationError);
      return;
    }

    setError('');
    onSubmit(url.trim());
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setUrl(value);
    
    if (touched) {
      const validationError = validateUrl(value);
      setError(validationError);
    }
  };

  const handleBlur = () => {
    setTouched(true);
    const validationError = validateUrl(url);
    setError(validationError);
  };

  const isFormDisabled = disabled || isLoading;
  const hasError = touched && error;

  return (
    <div className={cn('w-full max-w-2xl', className)}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label
            htmlFor="playlist-url"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            YouTube Playlist URL
          </label>
          
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <LinkIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
            </div>
            
            <input
              id="playlist-url"
              type="url"
              value={url}
              onChange={handleUrlChange}
              onBlur={handleBlur}
              disabled={isFormDisabled}
              placeholder="https://www.youtube.com/playlist?list=..."
              className={cn(
                'block w-full pl-10 pr-3 py-3 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors',
                hasError
                  ? 'border-red-300 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500'
                  : 'border-gray-300 text-gray-900',
                isFormDisabled && 'bg-gray-50 cursor-not-allowed'
              )}
              aria-invalid={hasError}
              aria-describedby={hasError ? 'url-error' : undefined}
            />
          </div>
          
          {hasError && (
            <InlineError
              message={error}
              className="mt-2"
              id="url-error"
            />
          )}
          
          <p className="mt-2 text-sm text-gray-500">
            Enter a YouTube playlist URL to download all videos in the playlist.
          </p>
        </div>

        <button
          type="submit"
          disabled={isFormDisabled || hasError}
          className={cn(
            'w-full flex items-center justify-center gap-2 px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-white transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
            isFormDisabled || hasError
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800'
          )}
          aria-label={isLoading ? 'Loading playlist...' : 'Download all videos'}
        >
          {isLoading ? (
            <>
              <LoadingSpinner size="sm" />
              <span>Loading Playlist...</span>
            </>
          ) : (
            <>
              <PlayIcon className="h-5 w-5" aria-hidden="true" />
              <span>Download All Videos</span>
            </>
          )}
        </button>
      </form>

      {/* Example URLs */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-2">
          Example playlist URLs:
        </h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• https://www.youtube.com/playlist?list=PLxxxxxx</li>
          <li>• https://youtube.com/watch?v=xxxxx&list=PLxxxxxx</li>
          <li>• https://youtu.be/xxxxx?list=PLxxxxxx</li>
        </ul>
      </div>
    </div>
  );
}
