#!/usr/bin/env node

/**
 * Test script to verify the application setup
 * Run with: node scripts/test-setup.js
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing YouTube Playlist Downloader Setup...\n');

// Test 1: Check required files exist
console.log('📁 Checking required files...');
const requiredFiles = [
  'package.json',
  'next.config.ts',
  'src/app/page.tsx',
  'src/app/layout.tsx',
  'src/app/api/playlist/route.ts',
  'src/app/api/download/route.ts',
  'src/lib/youtube.ts',
  'src/lib/utils.ts',
  '.env.local.example'
];

let missingFiles = [];
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file}`);
    missingFiles.push(file);
  }
});

if (missingFiles.length > 0) {
  console.log(`\n❌ Missing files: ${missingFiles.join(', ')}`);
  process.exit(1);
}

// Test 2: Check package.json dependencies
console.log('\n📦 Checking dependencies...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredDeps = [
  'next',
  'react',
  'react-dom',
  'ytdl-core',
  'googleapis',
  'axios',
  '@heroicons/react',
  'clsx',
  'tailwind-merge',
  'ffmpeg-static',
  'fluent-ffmpeg'
];

let missingDeps = [];
requiredDeps.forEach(dep => {
  if (packageJson.dependencies[dep] || packageJson.devDependencies[dep]) {
    console.log(`  ✅ ${dep}`);
  } else {
    console.log(`  ❌ ${dep}`);
    missingDeps.push(dep);
  }
});

if (missingDeps.length > 0) {
  console.log(`\n❌ Missing dependencies: ${missingDeps.join(', ')}`);
  console.log('Run: npm install ' + missingDeps.join(' '));
  process.exit(1);
}

// Test 3: Check environment variables
console.log('\n🔧 Checking environment configuration...');
if (fs.existsSync('.env.local')) {
  const envContent = fs.readFileSync('.env.local', 'utf8');
  
  if (envContent.includes('YOUTUBE_API_KEY=') && !envContent.includes('YOUTUBE_API_KEY=\n')) {
    console.log('  ✅ YOUTUBE_API_KEY is set');
  } else {
    console.log('  ⚠️  YOUTUBE_API_KEY is not configured');
    console.log('     Please add your YouTube API key to .env.local');
  }
  
  if (envContent.includes('NEXT_PUBLIC_APP_URL=')) {
    console.log('  ✅ NEXT_PUBLIC_APP_URL is set');
  } else {
    console.log('  ⚠️  NEXT_PUBLIC_APP_URL is not set (optional)');
  }
} else {
  console.log('  ⚠️  .env.local file not found');
  console.log('     Copy .env.local.example to .env.local and configure');
}

// Test 4: Check TypeScript configuration
console.log('\n📝 Checking TypeScript configuration...');
if (fs.existsSync('tsconfig.json')) {
  console.log('  ✅ tsconfig.json exists');
  
  try {
    const tsConfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
    if (tsConfig.compilerOptions && tsConfig.compilerOptions.paths) {
      console.log('  ✅ Path aliases configured');
    } else {
      console.log('  ⚠️  Path aliases not configured');
    }
  } catch (error) {
    console.log('  ❌ Invalid tsconfig.json');
  }
} else {
  console.log('  ❌ tsconfig.json not found');
}

// Test 5: Check Tailwind CSS configuration
console.log('\n🎨 Checking Tailwind CSS configuration...');
if (fs.existsSync('postcss.config.mjs')) {
  console.log('  ✅ PostCSS configuration exists');
} else {
  console.log('  ❌ PostCSS configuration missing');
}

if (fs.existsSync('src/app/globals.css')) {
  const cssContent = fs.readFileSync('src/app/globals.css', 'utf8');
  if (cssContent.includes('@import "tailwindcss"')) {
    console.log('  ✅ Tailwind CSS imported');
  } else {
    console.log('  ❌ Tailwind CSS not imported');
  }
} else {
  console.log('  ❌ globals.css not found');
}

console.log('\n🎉 Setup verification complete!');
console.log('\n📋 Next steps:');
console.log('1. Configure your YouTube API key in .env.local');
console.log('2. Run: npm run dev');
console.log('3. Open: http://localhost:3000');
console.log('4. Test with a public YouTube playlist');

console.log('\n📚 Documentation:');
console.log('- README.md for detailed setup instructions');
console.log('- .env.local.example for environment variable examples');
console.log('- API documentation in README.md');

console.log('\n⚠️  Remember:');
console.log('- Respect YouTube\'s Terms of Service');
console.log('- Only download content you have permission to use');
console.log('- Monitor your YouTube API quota usage');
