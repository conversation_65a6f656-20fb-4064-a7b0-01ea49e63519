'use client';

import { cn } from '@/lib/utils';
import { CheckCircleIcon, XCircleIcon, ClockIcon } from '@heroicons/react/24/outline';
import { LoadingSpinner } from './LoadingSpinner';

export interface DownloadProgress {
  videoId: string;
  title: string;
  status: 'pending' | 'downloading' | 'completed' | 'error';
  error?: string;
  progress?: number;
}

interface ProgressTrackerProps {
  downloads: DownloadProgress[];
  currentIndex: number;
  totalCount: number;
  className?: string;
}

/**
 * Progress tracker component for download queue
 */
export function ProgressTracker({
  downloads,
  currentIndex,
  totalCount,
  className
}: ProgressTrackerProps) {
  const completedCount = downloads.filter(d => d.status === 'completed').length;
  const errorCount = downloads.filter(d => d.status === 'error').length;
  const overallProgress = totalCount > 0 ? (completedCount / totalCount) * 100 : 0;

  return (
    <div className={cn('space-y-4', className)}>
      {/* Overall Progress */}
      <div className="bg-white rounded-lg border p-4 shadow-sm">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-semibold text-gray-900">
            Download Progress
          </h3>
          <span className="text-sm text-gray-600">
            {completedCount} of {totalCount} completed
          </span>
        </div>
        
        <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${overallProgress}%` }}
            role="progressbar"
            aria-valuenow={overallProgress}
            aria-valuemin={0}
            aria-valuemax={100}
            aria-label={`Overall progress: ${Math.round(overallProgress)}%`}
          />
        </div>
        
        <div className="flex justify-between text-xs text-gray-500">
          <span>{Math.round(overallProgress)}% complete</span>
          {errorCount > 0 && (
            <span className="text-red-600">{errorCount} failed</span>
          )}
        </div>
      </div>

      {/* Current Download Status */}
      {currentIndex < downloads.length && (
        <div className="bg-blue-50 rounded-lg border border-blue-200 p-4">
          <div className="flex items-center gap-3">
            <LoadingSpinner size="sm" />
            <div className="flex-1">
              <p className="text-sm font-medium text-blue-900">
                Downloading video {currentIndex + 1} of {totalCount}
              </p>
              <p className="text-sm text-blue-700 truncate">
                {downloads[currentIndex]?.title || 'Unknown title'}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Download List */}
      <div className="bg-white rounded-lg border shadow-sm">
        <div className="p-4 border-b">
          <h4 className="font-medium text-gray-900">Download Queue</h4>
        </div>
        <div className="max-h-64 overflow-y-auto">
          {downloads.map((download, index) => (
            <DownloadItem
              key={download.videoId}
              download={download}
              index={index}
              isActive={index === currentIndex}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

/**
 * Individual download item component
 */
function DownloadItem({
  download,
  index,
  isActive
}: {
  download: DownloadProgress;
  index: number;
  isActive: boolean;
}) {
  const getStatusIcon = () => {
    switch (download.status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'downloading':
        return <LoadingSpinner size="sm" />;
      case 'pending':
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = () => {
    switch (download.status) {
      case 'completed':
        return 'Completed';
      case 'error':
        return 'Failed';
      case 'downloading':
        return 'Downloading...';
      case 'pending':
      default:
        return 'Pending';
    }
  };

  const getStatusColor = () => {
    switch (download.status) {
      case 'completed':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      case 'downloading':
        return 'text-blue-600';
      case 'pending':
      default:
        return 'text-gray-500';
    }
  };

  return (
    <div
      className={cn(
        'flex items-center gap-3 p-3 border-b last:border-b-0',
        isActive && 'bg-blue-50',
        download.status === 'error' && 'bg-red-50'
      )}
    >
      <div className="flex-shrink-0">
        {getStatusIcon()}
      </div>
      
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate">
          {download.title}
        </p>
        <div className="flex items-center gap-2 mt-1">
          <span className={cn('text-xs', getStatusColor())}>
            {getStatusText()}
          </span>
          {download.error && (
            <span className="text-xs text-red-600 truncate">
              - {download.error}
            </span>
          )}
        </div>
      </div>
      
      <div className="flex-shrink-0 text-xs text-gray-500">
        #{index + 1}
      </div>
    </div>
  );
}
