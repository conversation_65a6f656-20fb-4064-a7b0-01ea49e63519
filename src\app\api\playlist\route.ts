import { NextRequest, NextResponse } from 'next/server';
import { extractPlaylistId, getPlaylistInfo } from '@/lib/youtube';
import { getErrorMessage, isQuotaError, isNetworkError } from '@/lib/utils';

/**
 * GET /api/playlist
 * 
 * Fetches playlist information and video list from YouTube Data API
 * 
 * Query Parameters:
 * - url: YouTube playlist URL or playlist ID
 * 
 * Returns:
 * - 200: Playlist information with video list
 * - 400: Invalid playlist URL or missing parameters
 * - 403: YouTube API quota exceeded
 * - 404: Playlist not found or private
 * - 500: Server error
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const url = searchParams.get('url');

    // Validate input
    if (!url) {
      return NextResponse.json(
        { 
          error: 'Missing required parameter: url',
          code: 'MISSING_URL'
        },
        { status: 400 }
      );
    }

    // Extract playlist ID
    const playlistId = extractPlaylistId(url);
    if (!playlistId) {
      return NextResponse.json(
        { 
          error: 'Invalid YouTube playlist URL. Please provide a valid playlist URL.',
          code: 'INVALID_URL'
        },
        { status: 400 }
      );
    }

    // Check if YouTube API key is configured
    if (!process.env.YOUTUBE_API_KEY) {
      return NextResponse.json(
        { 
          error: 'YouTube API key not configured. Please set YOUTUBE_API_KEY environment variable.',
          code: 'API_KEY_MISSING'
        },
        { status: 500 }
      );
    }

    // Fetch playlist information
    const playlistInfo = await getPlaylistInfo(playlistId);
    
    if (!playlistInfo) {
      return NextResponse.json(
        { 
          error: 'Playlist not found or is private. Please check the URL and try again.',
          code: 'PLAYLIST_NOT_FOUND'
        },
        { status: 404 }
      );
    }

    // Filter out unavailable videos
    const availableVideos = playlistInfo.videos.filter(video => 
      video.id && video.title !== 'Private video' && video.title !== 'Deleted video'
    );

    const response = {
      success: true,
      data: {
        ...playlistInfo,
        videos: availableVideos,
        availableVideoCount: availableVideos.length,
        unavailableVideoCount: playlistInfo.videos.length - availableVideos.length,
      }
    };

    return NextResponse.json(response, { status: 200 });

  } catch (error) {
    console.error('Playlist API error:', error);
    
    const errorMessage = getErrorMessage(error);
    
    // Handle specific error types
    if (isQuotaError(error)) {
      return NextResponse.json(
        { 
          error: 'YouTube API quota exceeded. Please try again later.',
          code: 'QUOTA_EXCEEDED',
          details: errorMessage
        },
        { status: 403 }
      );
    }
    
    if (isNetworkError(error)) {
      return NextResponse.json(
        { 
          error: 'Network error occurred. Please check your connection and try again.',
          code: 'NETWORK_ERROR',
          details: errorMessage
        },
        { status: 503 }
      );
    }
    
    // Generic server error
    return NextResponse.json(
      { 
        error: 'An unexpected error occurred while fetching playlist information.',
        code: 'SERVER_ERROR',
        details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/playlist
 * 
 * Alternative endpoint for playlist fetching with request body
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { url } = body;

    if (!url) {
      return NextResponse.json(
        { 
          error: 'Missing required field: url',
          code: 'MISSING_URL'
        },
        { status: 400 }
      );
    }

    // Redirect to GET method by creating a new URL with query parameters
    const searchParams = new URLSearchParams({ url });
    const getRequest = new NextRequest(
      `${request.nextUrl.origin}/api/playlist?${searchParams.toString()}`,
      { method: 'GET' }
    );

    return GET(getRequest);

  } catch (error) {
    console.error('Playlist POST API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Invalid request body. Expected JSON with url field.',
        code: 'INVALID_BODY'
      },
      { status: 400 }
    );
  }
}
