import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Utility function to merge Tailwind CSS classes
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Rate limiter for API requests
 */
class RateLimiter {
  private requests: number[] = [];
  private maxRequests: number;
  private timeWindow: number;

  constructor(maxRequests: number = 10, timeWindowMs: number = 60000) {
    this.maxRequests = maxRequests;
    this.timeWindow = timeWindowMs;
  }

  /**
   * Check if request is allowed under rate limit
   */
  isAllowed(): boolean {
    const now = Date.now();
    
    // Remove old requests outside time window
    this.requests = this.requests.filter(time => now - time < this.timeWindow);
    
    // Check if under limit
    if (this.requests.length < this.maxRequests) {
      this.requests.push(now);
      return true;
    }
    
    return false;
  }

  /**
   * Get time until next request is allowed (in ms)
   */
  getTimeUntilReset(): number {
    if (this.requests.length === 0) return 0;
    
    const oldestRequest = Math.min(...this.requests);
    const timeUntilReset = this.timeWindow - (Date.now() - oldestRequest);
    
    return Math.max(0, timeUntilReset);
  }
}

// Global rate limiter instance
export const downloadRateLimiter = new RateLimiter(
  parseInt(process.env.MAX_DOWNLOADS_PER_MINUTE || '10'),
  60000 // 1 minute
);

/**
 * Sanitize filename for safe file downloads
 */
export function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[<>:"/\\|?*]/g, '') // Remove invalid characters
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .trim()
    .substring(0, 200); // Limit length
}

/**
 * Format file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Format time duration in human readable format
 */
export function formatTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
}

/**
 * Delay execution for specified milliseconds
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry function with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (i === maxRetries) {
        throw lastError;
      }
      
      // Exponential backoff with jitter
      const delayMs = baseDelay * Math.pow(2, i) + Math.random() * 1000;
      await delay(delayMs);
    }
  }
  
  throw lastError!;
}

/**
 * Validate YouTube URL format
 */
export function isValidYouTubeUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    return (
      urlObj.hostname.includes('youtube.com') ||
      urlObj.hostname.includes('youtu.be')
    );
  } catch {
    return false;
  }
}

/**
 * Extract error message from various error types
 */
export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message);
  }
  
  return 'An unknown error occurred';
}

/**
 * Check if error is a YouTube API quota error
 */
export function isQuotaError(error: any): boolean {
  return (
    error?.code === 403 ||
    error?.message?.includes('quota') ||
    error?.message?.includes('Quota')
  );
}

/**
 * Check if error is a network/timeout error
 */
export function isNetworkError(error: any): boolean {
  return (
    error?.code === 'ENOTFOUND' ||
    error?.code === 'ETIMEDOUT' ||
    error?.code === 'ECONNRESET' ||
    error?.message?.includes('timeout') ||
    error?.message?.includes('network')
  );
}

/**
 * Validation utilities
 */
export const validation = {
  /**
   * Validate YouTube playlist URL
   */
  playlistUrl: (url: string): { isValid: boolean; error?: string } => {
    if (!url || !url.trim()) {
      return { isValid: false, error: 'URL is required' };
    }

    if (!isValidYouTubeUrl(url)) {
      return { isValid: false, error: 'Please enter a valid YouTube URL' };
    }

    if (!url.includes('list=') && !url.includes('playlist')) {
      return {
        isValid: false,
        error: 'Please enter a playlist URL (should contain "list=" parameter)'
      };
    }

    return { isValid: true };
  },

  /**
   * Validate YouTube video URL
   */
  videoUrl: (url: string): { isValid: boolean; error?: string } => {
    if (!url || !url.trim()) {
      return { isValid: false, error: 'URL is required' };
    }

    if (!isValidYouTubeUrl(url)) {
      return { isValid: false, error: 'Please enter a valid YouTube URL' };
    }

    return { isValid: true };
  },

  /**
   * Validate API key format
   */
  apiKey: (key: string): { isValid: boolean; error?: string } => {
    if (!key || !key.trim()) {
      return { isValid: false, error: 'API key is required' };
    }

    if (key.length < 20) {
      return { isValid: false, error: 'API key appears to be too short' };
    }

    return { isValid: true };
  },
};

/**
 * Sanitize and validate environment variables
 */
export function validateEnvironment(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check YouTube API key
  if (!process.env.YOUTUBE_API_KEY) {
    errors.push('YOUTUBE_API_KEY environment variable is not set');
  } else {
    const apiKeyValidation = validation.apiKey(process.env.YOUTUBE_API_KEY);
    if (!apiKeyValidation.isValid) {
      errors.push(`Invalid YOUTUBE_API_KEY: ${apiKeyValidation.error}`);
    }
  }

  // Check rate limiting settings
  const maxDownloads = parseInt(process.env.MAX_DOWNLOADS_PER_MINUTE || '10');
  if (isNaN(maxDownloads) || maxDownloads < 1 || maxDownloads > 100) {
    errors.push('MAX_DOWNLOADS_PER_MINUTE must be a number between 1 and 100');
  }

  const downloadTimeout = parseInt(process.env.DOWNLOAD_TIMEOUT_SECONDS || '300');
  if (isNaN(downloadTimeout) || downloadTimeout < 30 || downloadTimeout > 3600) {
    errors.push('DOWNLOAD_TIMEOUT_SECONDS must be a number between 30 and 3600');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
