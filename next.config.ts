import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // External packages for server components
  serverExternalPackages: ['ytdl-core'],

  // Configure headers for video streaming
  async headers() {
    return [
      {
        source: '/api/download',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'Content-Type',
            value: 'video/mp4',
          },
        ],
      },
    ];
  },

  // Enable standalone output for Docker
  output: 'standalone',

  // Optimize images
  images: {
    domains: ['i.ytimg.com', 'img.youtube.com'],
    formats: ['image/webp', 'image/avif'],
  },

  // Compress responses
  compress: true,
};

export default nextConfig;
