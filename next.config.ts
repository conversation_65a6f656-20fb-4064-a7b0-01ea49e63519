import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Enable experimental features for better streaming support
  experimental: {
    serverComponentsExternalPackages: ['ytdl-core'],
  },

  // Configure headers for video streaming
  async headers() {
    return [
      {
        source: '/api/download',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'Content-Type',
            value: 'video/mp4',
          },
        ],
      },
    ];
  },

  // Increase API timeout for large video downloads
  api: {
    responseLimit: false,
  },
};

export default nextConfig;
