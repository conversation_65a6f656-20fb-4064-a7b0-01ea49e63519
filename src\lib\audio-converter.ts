import ffmpeg from 'fluent-ffmpeg';
import ffmpegStatic from 'ffmpeg-static';
import { Readable, PassThrough } from 'stream';
import { AudioQuality, getAudioBitrate } from './youtube';

// Set ffmpeg path
if (ffmpegStatic) {
  ffmpeg.setFfmpegPath(ffmpegStatic);
}

/**
 * Convert video stream to MP3 audio stream
 * @param inputStream - Input video stream
 * @param quality - Audio quality
 * @returns Promise<Readable> - MP3 audio stream
 */
export function convertToMp3(inputStream: Readable, quality: AudioQuality): Promise<Readable> {
  return new Promise((resolve, reject) => {
    const outputStream = new PassThrough();
    const bitrate = getAudioBitrate(quality);

    const command = ffmpeg(inputStream)
      .audioCodec('libmp3lame')
      .audioBitrate(bitrate)
      .format('mp3')
      .audioChannels(2)
      .audioFrequency(44100)
      .on('start', (commandLine) => {
        console.log('FFmpeg started with command:', commandLine);
      })
      .on('progress', (progress) => {
        console.log('Conversion progress:', progress.percent + '%');
      })
      .on('end', () => {
        console.log('MP3 conversion completed');
        outputStream.end();
      })
      .on('error', (err) => {
        console.error('FFmpeg error:', err);
        reject(err);
      });

    // Pipe to output stream
    command.pipe(outputStream, { end: true });
    
    resolve(outputStream);
  });
}

/**
 * Get estimated file size for MP3 conversion
 * @param durationSeconds - Video duration in seconds
 * @param quality - Audio quality
 * @returns Estimated file size in bytes
 */
export function getEstimatedMp3Size(durationSeconds: number, quality: AudioQuality): number {
  const bitrate = getAudioBitrate(quality);
  // Formula: (bitrate in kbps * duration in seconds) / 8 * 1024
  return Math.round((bitrate * durationSeconds) / 8 * 1024);
}

/**
 * Validate if ffmpeg is available
 * @returns Promise<boolean> - True if ffmpeg is available
 */
export function validateFfmpeg(): Promise<boolean> {
  return new Promise((resolve) => {
    ffmpeg.getAvailableFormats((err, formats) => {
      if (err) {
        console.error('FFmpeg validation failed:', err);
        resolve(false);
      } else {
        console.log('FFmpeg is available with formats:', Object.keys(formats || {}).length);
        resolve(true);
      }
    });
  });
}

/**
 * Get supported audio qualities
 * @returns Array of supported audio qualities
 */
export function getSupportedAudioQualities(): AudioQuality[] {
  return ['320kbps', '256kbps', '192kbps', '128kbps', '96kbps', '64kbps'];
}

/**
 * Get supported video qualities
 * @returns Array of supported video qualities
 */
export function getSupportedVideoQualities(): string[] {
  return ['1080p', '720p', '480p', '360p', '240p', '144p'];
}

/**
 * Check if quality is valid for format
 * @param format - Download format
 * @param quality - Quality string
 * @returns Boolean indicating if combination is valid
 */
export function isValidQualityForFormat(format: string, quality: string): boolean {
  if (format === 'mp4') {
    return getSupportedVideoQualities().includes(quality);
  }
  if (format === 'mp3') {
    return getSupportedAudioQualities().includes(quality as AudioQuality);
  }
  return false;
}
