'use client';

import { VideoMetadata } from '@/lib/youtube';
import { cn } from '@/lib/utils';
import { PlayIcon, ClockIcon, UserIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';

interface VideoListProps {
  videos: VideoMetadata[];
  playlistTitle: string;
  onDownloadAll: () => void;
  isDownloading?: boolean;
  className?: string;
}

/**
 * Video list component displaying playlist videos
 */
export function VideoList({
  videos,
  playlistTitle,
  onDownloadAll,
  isDownloading = false,
  className
}: VideoListProps) {
  const totalDuration = videos.length > 0 ? `${videos.length} videos` : 'No videos';

  return (
    <div className={cn('w-full max-w-4xl', className)}>
      {/* Playlist Header */}
      <div className="bg-white rounded-lg border shadow-sm p-6 mb-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h2 className="text-xl font-bold text-gray-900 mb-2">
              {playlistTitle}
            </h2>
            <p className="text-sm text-gray-600 mb-4">
              {totalDuration}
            </p>
            
            <button
              onClick={onDownloadAll}
              disabled={isDownloading || videos.length === 0}
              className={cn(
                'inline-flex items-center gap-2 px-6 py-3 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white transition-all duration-200',
                'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
                isDownloading || videos.length === 0
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800'
              )}
              aria-label={isDownloading ? 'Downloading...' : 'Start downloading all videos'}
            >
              <PlayIcon className="h-4 w-4" aria-hidden="true" />
              {isDownloading ? 'Downloading...' : 'Download All Videos'}
            </button>
          </div>
        </div>
      </div>

      {/* Video List */}
      {videos.length > 0 ? (
        <div className="bg-white rounded-lg border shadow-sm">
          <div className="p-4 border-b">
            <h3 className="font-medium text-gray-900">Videos in Playlist</h3>
          </div>
          
          <div className="divide-y divide-gray-200">
            {videos.map((video, index) => (
              <VideoItem
                key={video.id}
                video={video}
                index={index}
              />
            ))}
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg border shadow-sm p-8 text-center">
          <div className="text-gray-400 mb-4">
            <PlayIcon className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No videos found
          </h3>
          <p className="text-gray-600">
            This playlist appears to be empty or all videos are private/unavailable.
          </p>
        </div>
      )}
    </div>
  );
}

/**
 * Individual video item component
 */
function VideoItem({
  video,
  index
}: {
  video: VideoMetadata;
  index: number;
}) {
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    e.currentTarget.src = '/placeholder-video.jpg';
  };

  return (
    <div className="p-4 hover:bg-gray-50 transition-colors">
      <div className="flex items-start gap-4">
        {/* Video Index */}
        <div className="flex-shrink-0 w-8 text-center">
          <span className="text-sm font-medium text-gray-500">
            {index + 1}
          </span>
        </div>

        {/* Video Thumbnail */}
        <div className="flex-shrink-0 relative">
          <div className="w-32 h-18 bg-gray-200 rounded overflow-hidden">
            {video.thumbnail ? (
              <Image
                src={video.thumbnail}
                alt={`Thumbnail for ${video.title}`}
                width={128}
                height={72}
                className="w-full h-full object-cover"
                onError={handleImageError}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <PlayIcon className="h-6 w-6 text-gray-400" />
              </div>
            )}
          </div>
          
          {/* Duration overlay */}
          {video.duration && video.duration !== 'Unknown' && (
            <div className="absolute bottom-1 right-1 bg-black bg-opacity-75 text-white text-xs px-1 py-0.5 rounded">
              {video.duration}
            </div>
          )}
        </div>

        {/* Video Info */}
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-gray-900 line-clamp-2 mb-1">
            {video.title}
          </h4>
          
          <div className="flex items-center gap-4 text-xs text-gray-500">
            {video.channelTitle && (
              <div className="flex items-center gap-1">
                <UserIcon className="h-3 w-3" />
                <span className="truncate max-w-32">
                  {video.channelTitle}
                </span>
              </div>
            )}
            
            {video.duration && video.duration !== 'Unknown' && (
              <div className="flex items-center gap-1">
                <ClockIcon className="h-3 w-3" />
                <span>{video.duration}</span>
              </div>
            )}
            
            {video.publishedAt && (
              <span>
                {new Date(video.publishedAt).toLocaleDateString()}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
