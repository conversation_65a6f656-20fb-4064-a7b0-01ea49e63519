/**
 * Utility functions for generating dynamic download button text
 */

/**
 * Get the appropriate download button text based on format
 */
export function getDownloadButtonText(format: 'mp4' | 'mp3', isLoading = false): string {
  if (isLoading) {
    return format === 'mp4' ? 'Downloading Videos...' : 'Downloading Audio...';
  }
  
  return format === 'mp4' ? 'Download All Videos' : 'Download All Audio';
}

/**
 * Get the appropriate aria-label for download button
 */
export function getDownloadButtonAriaLabel(format: 'mp4' | 'mp3', isLoading = false): string {
  if (isLoading) {
    return format === 'mp4' ? 'Downloading videos...' : 'Downloading audio files...';
  }
  
  return format === 'mp4' ? 'Download all videos' : 'Download all audio files';
}

/**
 * Get the appropriate loading text for the form
 */
export function getLoadingText(format: 'mp4' | 'mp3'): string {
  return 'Loading Playlist...';
}

/**
 * Get the appropriate content type description
 */
export function getContentTypeDescription(format: 'mp4' | 'mp3'): string {
  return format === 'mp4' 
    ? 'Enter a YouTube playlist URL to download all videos in the playlist.'
    : 'Enter a YouTube playlist URL to download all audio from the playlist.';
}
