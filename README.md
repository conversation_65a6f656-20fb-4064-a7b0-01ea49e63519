# YouTube Playlist Downloader

A modern, responsive web application built with Next.js 15 and React 19 that allows users to download all videos from any YouTube playlist with ease.

## ✨ Features

- **Playlist Parsing**: Extract video information from any YouTube playlist URL
- **Batch Downloads**: Download all videos in a playlist sequentially
- **Progress Tracking**: Real-time progress indicators and download status
- **Error Handling**: Comprehensive error handling for various scenarios
- **Responsive Design**: Mobile-friendly interface with Tailwind CSS
- **Accessibility**: WCAG compliant with proper ARIA labels and keyboard navigation
- **Rate Limiting**: Built-in rate limiting to prevent API abuse
- **SEO Optimized**: Proper meta tags and structured data

## 🚀 Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS v4
- **Icons**: Heroicons
- **YouTube Integration**: YouTube Data API v3, ytdl-core
- **HTTP Client**: Axios
- **Deployment**: Vercel (recommended)

## 📋 Prerequisites

Before running this application, you need:

1. **Node.js** (v18 or higher)
2. **YouTube Data API v3 Key** from [Google Cloud Console](https://console.developers.google.com/)

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd playlist-downloader
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```

   Edit `.env.local` and add your YouTube API key:
   ```env
   YOUTUBE_API_KEY=your_youtube_api_key_here
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🔑 Getting YouTube API Key

1. Go to [Google Cloud Console](https://console.developers.google.com/)
2. Create a new project or select existing one
3. Enable the **YouTube Data API v3**
4. Create credentials (API Key)
5. Restrict the API key to YouTube Data API v3 (recommended)
6. Copy the API key to your `.env.local` file

## 📖 Usage

1. **Enter Playlist URL**: Paste any YouTube playlist URL into the input field
2. **Load Playlist**: Click "Download All Videos" to fetch playlist information
3. **Review Videos**: See all videos in the playlist with thumbnails and metadata
4. **Start Download**: Click "Download All Videos" to begin sequential downloads
5. **Monitor Progress**: Track download progress in real-time

### Supported URL Formats

- `https://www.youtube.com/playlist?list=PLxxxxxx`
- `https://youtube.com/watch?v=xxxxx&list=PLxxxxxx`
- `https://youtu.be/xxxxx?list=PLxxxxxx`

## 🏗️ Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   │   ├── download/      # Video download endpoint
│   │   └── playlist/      # Playlist parsing endpoint
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Main page
├── components/            # React components
│   ├── ErrorBoundary.tsx  # Error boundary wrapper
│   ├── ErrorDisplay.tsx   # Error message component
│   ├── LoadingSpinner.tsx # Loading indicators
│   ├── PlaylistForm.tsx   # URL input form
│   ├── ProgressTracker.tsx # Download progress
│   └── VideoList.tsx      # Video list display
└── lib/                   # Utility functions
    ├── utils.ts           # General utilities
    └── youtube.ts         # YouTube API helpers
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `YOUTUBE_API_KEY` | YouTube Data API v3 key | - | ✅ |
| `NEXT_PUBLIC_APP_URL` | Application URL | `http://localhost:3000` | ❌ |
| `MAX_DOWNLOADS_PER_MINUTE` | Rate limit for downloads | `10` | ❌ |
| `DOWNLOAD_TIMEOUT_SECONDS` | Download timeout | `300` | ❌ |

### API Endpoints

#### GET `/api/playlist`
Fetch playlist information and video list.

**Query Parameters:**
- `url` (required): YouTube playlist URL

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "playlist_id",
    "title": "Playlist Title",
    "description": "Playlist description",
    "videoCount": 10,
    "videos": [...]
  }
}
```

#### GET `/api/download`
Download a YouTube video as MP4 file.

**Query Parameters:**
- `videoUrl` (required): YouTube video URL
- `quality` (optional): Video quality preference

**Response:** Video file stream with appropriate headers

## 🚨 Error Handling

The application handles various error scenarios:

### Common Errors

| Error | Description | Solution |
|-------|-------------|----------|
| `MISSING_URL` | No playlist URL provided | Enter a valid YouTube playlist URL |
| `INVALID_URL` | Invalid YouTube URL format | Check URL format and try again |
| `PLAYLIST_NOT_FOUND` | Playlist not found or private | Verify playlist exists and is public |
| `QUOTA_EXCEEDED` | YouTube API quota exceeded | Wait and try again later |
| `RATE_LIMIT_EXCEEDED` | Too many download requests | Wait before downloading more videos |
| `VIDEO_UNAVAILABLE` | Video is private/deleted | Skip unavailable videos |
| `NETWORK_ERROR` | Connection issues | Check internet connection |

### Error Recovery

- **Automatic Retry**: Network errors are automatically retried with exponential backoff
- **Graceful Degradation**: Unavailable videos are skipped, others continue downloading
- **User Feedback**: Clear error messages with actionable solutions

## 🔒 Security & Privacy

- **API Key Protection**: YouTube API key is server-side only
- **Rate Limiting**: Prevents abuse and respects API quotas
- **Input Validation**: All user inputs are validated and sanitized
- **CORS Protection**: API routes are protected against unauthorized access
- **No Data Storage**: No user data or video content is stored on servers

## 🎯 Performance Optimization

- **Lazy Loading**: Components and images load on demand
- **Streaming Downloads**: Videos stream directly to user's browser
- **Memory Management**: Efficient handling of large video files
- **Caching**: API responses cached to reduce redundant requests
- **Bundle Optimization**: Code splitting and tree shaking enabled

## 📱 Browser Support

- **Chrome** 90+
- **Firefox** 88+
- **Safari** 14+
- **Edge** 90+

## 🚀 Deployment

### Vercel (Recommended)

1. **Connect Repository**
   ```bash
   vercel --prod
   ```

2. **Set Environment Variables**
   - Add `YOUTUBE_API_KEY` in Vercel dashboard
   - Set `NEXT_PUBLIC_APP_URL` to your domain

3. **Deploy**
   ```bash
   vercel deploy --prod
   ```

### Docker

1. **Build Image**
   ```bash
   docker build -t playlist-downloader .
   ```

2. **Run Container**
   ```bash
   docker run -p 3000:3000 -e YOUTUBE_API_KEY=your_key playlist-downloader
   ```

### Manual Deployment

1. **Build Application**
   ```bash
   npm run build
   ```

2. **Start Production Server**
   ```bash
   npm start
   ```

## 🧪 Testing

### Manual Testing Checklist

- [ ] Load playlist with various URL formats
- [ ] Handle private/deleted videos gracefully
- [ ] Download videos of different qualities
- [ ] Test error scenarios (invalid URLs, network issues)
- [ ] Verify responsive design on mobile devices
- [ ] Check accessibility with screen readers
- [ ] Test rate limiting functionality

### Test Playlists

Use these public playlists for testing:

- **Small Playlist** (3-5 videos): Test basic functionality
- **Large Playlist** (50+ videos): Test pagination and performance
- **Mixed Content**: Playlist with some private/deleted videos

## 🤝 Contributing

1. **Fork the repository**
2. **Create feature branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **Commit changes**
   ```bash
   git commit -m 'Add amazing feature'
   ```
4. **Push to branch**
   ```bash
   git push origin feature/amazing-feature
   ```
5. **Open Pull Request**

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Legal Notice

**Important**: This tool is for educational and personal use only. Users are responsible for:

- Respecting YouTube's Terms of Service
- Obtaining proper permissions for downloaded content
- Complying with copyright laws in their jurisdiction
- Using downloaded content in accordance with applicable licenses

The developers are not responsible for any misuse of this application.

## 🆘 Support

- **Documentation**: Check this README and inline code comments
- **Issues**: Report bugs via GitHub Issues
- **Discussions**: Use GitHub Discussions for questions
- **API Limits**: Monitor your YouTube API quota usage

## 🙏 Acknowledgments

- **Next.js Team** for the amazing framework
- **YouTube Data API** for playlist access
- **ytdl-core** for video download functionality
- **Tailwind CSS** for styling utilities
- **Heroicons** for beautiful icons

---

**Made with ❤️ using Next.js 15 and React 19**
