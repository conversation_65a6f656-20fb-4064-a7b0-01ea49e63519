'use client';

import { cn } from '@/lib/utils';
import { XCircleIcon, ExclamationTriangleIcon, InformationCircleIcon } from '@heroicons/react/24/outline';

interface ErrorDisplayProps {
  title?: string;
  message: string;
  type?: 'error' | 'warning' | 'info';
  className?: string;
  onDismiss?: () => void;
  details?: string;
  showDetails?: boolean;
}

/**
 * Error display component with different severity levels
 */
export function ErrorDisplay({
  title,
  message,
  type = 'error',
  className,
  onDismiss,
  details,
  showDetails = false
}: ErrorDisplayProps) {
  const typeConfig = {
    error: {
      icon: XCircleIcon,
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      iconColor: 'text-red-500',
      titleColor: 'text-red-800',
      messageColor: 'text-red-700'
    },
    warning: {
      icon: ExclamationTriangleIcon,
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      iconColor: 'text-yellow-500',
      titleColor: 'text-yellow-800',
      messageColor: 'text-yellow-700'
    },
    info: {
      icon: InformationCircleIcon,
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      iconColor: 'text-blue-500',
      titleColor: 'text-blue-800',
      messageColor: 'text-blue-700'
    }
  };

  const config = typeConfig[type];
  const Icon = config.icon;

  return (
    <div
      className={cn(
        'rounded-lg border p-4',
        config.bgColor,
        config.borderColor,
        className
      )}
      role="alert"
      aria-live="polite"
    >
      <div className="flex items-start">
        <Icon
          className={cn('h-5 w-5 flex-shrink-0 mt-0.5', config.iconColor)}
          aria-hidden="true"
        />
        <div className="ml-3 flex-1">
          {title && (
            <h3 className={cn('text-sm font-medium', config.titleColor)}>
              {title}
            </h3>
          )}
          <p className={cn('text-sm', title ? 'mt-1' : '', config.messageColor)}>
            {message}
          </p>
          {details && showDetails && (
            <details className="mt-2">
              <summary className={cn('text-xs cursor-pointer', config.messageColor)}>
                Show technical details
              </summary>
              <pre className={cn('text-xs mt-1 whitespace-pre-wrap', config.messageColor)}>
                {details}
              </pre>
            </details>
          )}
        </div>
        {onDismiss && (
          <button
            type="button"
            className={cn(
              'ml-3 flex-shrink-0 rounded-md p-1.5 hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-offset-2',
              config.iconColor,
              'hover:bg-gray-200 focus:ring-gray-500'
            )}
            onClick={onDismiss}
            aria-label="Dismiss"
          >
            <XCircleIcon className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
}

/**
 * Simple inline error message
 */
export function InlineError({ 
  message, 
  className 
}: { 
  message: string; 
  className?: string; 
}) {
  return (
    <p className={cn('text-sm text-red-600', className)} role="alert">
      {message}
    </p>
  );
}
