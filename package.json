{"name": "playlist-downloader", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test:setup": "node scripts/test-setup.js", "docker:build": "docker build -t playlist-downloader .", "docker:run": "docker run -p 3000:3000 --env-file .env.local playlist-downloader", "type-check": "tsc --noEmit"}, "dependencies": {"@heroicons/react": "^2.2.0", "axios": "^1.10.0", "clsx": "^2.1.1", "googleapis": "^153.0.0", "next": "15.4.2", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "ytdl-core": "^4.11.5"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}