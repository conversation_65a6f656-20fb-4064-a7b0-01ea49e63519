import { google } from 'googleapis';
import ytdl from 'ytdl-core';

/**
 * Download format types
 */
export type DownloadFormat = 'mp4' | 'mp3';

/**
 * Video quality options
 */
export type VideoQuality = '1080p' | '720p' | '480p' | '360p' | '240p' | '144p';

/**
 * Audio quality options (bitrate)
 */
export type AudioQuality = '320kbps' | '256kbps' | '192kbps' | '128kbps' | '96kbps' | '64kbps';

/**
 * Download options interface
 */
export interface DownloadOptions {
  format: DownloadFormat;
  quality: VideoQuality | AudioQuality;
}

/**
 * YouTube video metadata interface
 */
export interface VideoMetadata {
  id: string;
  title: string;
  duration: string;
  thumbnail: string;
  channelTitle: string;
  publishedAt: string;
  downloadOptions?: DownloadOptions;
}

/**
 * Playlist information interface
 */
export interface PlaylistInfo {
  id: string;
  title: string;
  description: string;
  videoCount: number;
  videos: VideoMetadata[];
}

/**
 * YouTube API error interface
 */
export interface YouTubeError {
  code: string;
  message: string;
  details?: any;
}

/**
 * Initialize YouTube Data API v3 client
 */
const youtube = google.youtube({
  version: 'v3',
  auth: process.env.YOUTUBE_API_KEY,
});

/**
 * Extract playlist ID from various YouTube URL formats
 * @param url - YouTube playlist URL
 * @returns Playlist ID or null if invalid
 */
export function extractPlaylistId(url: string): string | null {
  try {
    const urlObj = new URL(url);
    
    // Handle different YouTube URL formats
    if (urlObj.hostname.includes('youtube.com')) {
      return urlObj.searchParams.get('list');
    }
    
    if (urlObj.hostname.includes('youtu.be')) {
      // For youtu.be links with playlist parameter
      return urlObj.searchParams.get('list');
    }
    
    // Direct playlist ID
    if (url.match(/^[A-Za-z0-9_-]{34}$/)) {
      return url;
    }
    
    return null;
  } catch {
    return null;
  }
}

/**
 * Extract video ID from YouTube URL
 * @param url - YouTube video URL
 * @returns Video ID or null if invalid
 */
export function extractVideoId(url: string): string | null {
  try {
    const urlObj = new URL(url);
    
    if (urlObj.hostname.includes('youtube.com')) {
      return urlObj.searchParams.get('v');
    }
    
    if (urlObj.hostname.includes('youtu.be')) {
      return urlObj.pathname.slice(1);
    }
    
    // Direct video ID
    if (url.match(/^[A-Za-z0-9_-]{11}$/)) {
      return url;
    }
    
    return null;
  } catch {
    return null;
  }
}

/**
 * Validate YouTube video URL using ytdl-core
 * @param url - YouTube video URL
 * @returns Promise<boolean> - True if valid and accessible
 */
export async function validateVideoUrl(url: string): Promise<boolean> {
  try {
    return ytdl.validateURL(url);
  } catch {
    return false;
  }
}

/**
 * Get video information using ytdl-core
 * @param videoId - YouTube video ID
 * @returns Promise<ytdl.videoInfo | null> - Video info or null if error
 */
export async function getVideoInfo(videoId: string): Promise<ytdl.videoInfo | null> {
  try {
    const url = `https://www.youtube.com/watch?v=${videoId}`;
    return await ytdl.getInfo(url);
  } catch {
    return null;
  }
}

/**
 * Get available video qualities for a video
 * @param videoInfo - Video information from ytdl
 * @returns Array of available video qualities
 */
export function getAvailableVideoQualities(videoInfo: ytdl.videoInfo): VideoQuality[] {
  const qualities: VideoQuality[] = [];
  const formats = videoInfo.formats;

  const qualityMap: Record<string, VideoQuality> = {
    '1080': '1080p',
    '720': '720p',
    '480': '480p',
    '360': '360p',
    '240': '240p',
    '144': '144p'
  };

  formats.forEach(format => {
    if (format.hasVideo && format.height) {
      const quality = qualityMap[format.height.toString()];
      if (quality && !qualities.includes(quality)) {
        qualities.push(quality);
      }
    }
  });

  // Sort by quality (highest first)
  return qualities.sort((a, b) => {
    const aNum = parseInt(a.replace('p', ''));
    const bNum = parseInt(b.replace('p', ''));
    return bNum - aNum;
  });
}

/**
 * Get the best available format for given quality and format type
 * @param videoInfo - Video information from ytdl
 * @param format - Desired format (mp4/mp3)
 * @param quality - Desired quality
 * @returns Best matching format or null
 */
export function getBestFormat(
  videoInfo: ytdl.videoInfo,
  format: DownloadFormat,
  quality: VideoQuality | AudioQuality
): ytdl.videoFormat | null {
  const formats = videoInfo.formats;

  if (format === 'mp4') {
    const targetHeight = parseInt((quality as VideoQuality).replace('p', ''));

    // First try to find a format with both video and audio at the exact quality
    let bestFormat = formats.find(f =>
      f.hasVideo && f.hasAudio && f.container === 'mp4' && f.height === targetHeight
    );

    if (bestFormat) return bestFormat;

    // Fallback to video-only format at the exact quality
    bestFormat = formats.find(f =>
      f.hasVideo && f.height === targetHeight && f.container === 'mp4'
    );

    if (bestFormat) return bestFormat;

    // Fallback to closest quality
    const availableFormats = formats
      .filter(f => f.hasVideo && f.height && f.container === 'mp4')
      .sort((a, b) => Math.abs((a.height || 0) - targetHeight) - Math.abs((b.height || 0) - targetHeight));

    return availableFormats[0] || null;
  }

  if (format === 'mp3') {
    // For MP3, we need audio-only or any format with audio that we can convert
    const targetBitrate = parseInt((quality as AudioQuality).replace('kbps', ''));

    // Find best audio format
    const audioFormats = formats
      .filter(f => f.hasAudio && !f.hasVideo)
      .sort((a, b) => {
        const aBitrate = a.audioBitrate || 0;
        const bBitrate = b.audioBitrate || 0;
        return Math.abs(aBitrate - targetBitrate) - Math.abs(bBitrate - targetBitrate);
      });

    if (audioFormats.length > 0) return audioFormats[0];

    // Fallback to any format with audio
    const anyAudioFormat = formats.find(f => f.hasAudio);
    return anyAudioFormat || null;
  }

  return null;
}

/**
 * Convert audio quality to ffmpeg bitrate
 * @param quality - Audio quality string
 * @returns Bitrate number for ffmpeg
 */
export function getAudioBitrate(quality: AudioQuality): number {
  return parseInt(quality.replace('kbps', ''));
}

/**
 * Get default download options
 * @returns Default download options
 */
export function getDefaultDownloadOptions(): DownloadOptions {
  return {
    format: 'mp4',
    quality: '720p'
  };
}

/**
 * Format duration from ISO 8601 to readable format
 * @param duration - ISO 8601 duration string (PT4M13S)
 * @returns Formatted duration string (4:13)
 */
export function formatDuration(duration: string): string {
  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
  if (!match) return '0:00';
  
  const hours = parseInt(match[1] || '0');
  const minutes = parseInt(match[2] || '0');
  const seconds = parseInt(match[3] || '0');
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
  
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

/**
 * Fetch playlist information and videos from YouTube Data API
 * @param playlistId - YouTube playlist ID
 * @returns Promise<PlaylistInfo | null> - Playlist info or null if error
 */
export async function getPlaylistInfo(playlistId: string): Promise<PlaylistInfo | null> {
  try {
    // Get playlist details
    const playlistResponse = await youtube.playlists.list({
      part: ['snippet', 'contentDetails'],
      id: [playlistId],
    });

    if (!playlistResponse.data.items || playlistResponse.data.items.length === 0) {
      return null;
    }

    const playlist = playlistResponse.data.items[0];
    const videoCount = playlist.contentDetails?.itemCount || 0;

    // Get all videos in the playlist (handle pagination)
    const videos: VideoMetadata[] = [];
    let nextPageToken: string | undefined;

    do {
      const videosResponse = await youtube.playlistItems.list({
        part: ['snippet', 'contentDetails'],
        playlistId: playlistId,
        maxResults: 50,
        pageToken: nextPageToken,
      });

      if (videosResponse.data.items) {
        for (const item of videosResponse.data.items) {
          const snippet = item.snippet;
          if (snippet && snippet.resourceId?.videoId) {
            videos.push({
              id: snippet.resourceId.videoId,
              title: snippet.title || 'Unknown Title',
              duration: 'Unknown', // Will be filled by video details API
              thumbnail: snippet.thumbnails?.medium?.url || '',
              channelTitle: snippet.videoOwnerChannelTitle || snippet.channelTitle || 'Unknown Channel',
              publishedAt: snippet.publishedAt || '',
            });
          }
        }
      }

      nextPageToken = videosResponse.data.nextPageToken || undefined;
    } while (nextPageToken);

    // Get detailed video information for durations
    if (videos.length > 0) {
      const videoIds = videos.map(v => v.id);
      const chunks = [];
      
      // Process in chunks of 50 (API limit)
      for (let i = 0; i < videoIds.length; i += 50) {
        chunks.push(videoIds.slice(i, i + 50));
      }

      for (const chunk of chunks) {
        const videoDetailsResponse = await youtube.videos.list({
          part: ['contentDetails'],
          id: chunk,
        });

        if (videoDetailsResponse.data.items) {
          videoDetailsResponse.data.items.forEach((video) => {
            const videoIndex = videos.findIndex(v => v.id === video.id);
            if (videoIndex !== -1 && video.contentDetails?.duration) {
              videos[videoIndex].duration = formatDuration(video.contentDetails.duration);
            }
          });
        }
      }
    }

    return {
      id: playlistId,
      title: playlist.snippet?.title || 'Unknown Playlist',
      description: playlist.snippet?.description || '',
      videoCount,
      videos,
    };
  } catch (error) {
    console.error('Error fetching playlist info:', error);
    return null;
  }
}
