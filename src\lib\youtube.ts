import { google } from 'googleapis';
import ytdl from 'ytdl-core';

/**
 * YouTube video metadata interface
 */
export interface VideoMetadata {
  id: string;
  title: string;
  duration: string;
  thumbnail: string;
  channelTitle: string;
  publishedAt: string;
}

/**
 * Playlist information interface
 */
export interface PlaylistInfo {
  id: string;
  title: string;
  description: string;
  videoCount: number;
  videos: VideoMetadata[];
}

/**
 * YouTube API error interface
 */
export interface YouTubeError {
  code: string;
  message: string;
  details?: any;
}

/**
 * Initialize YouTube Data API v3 client
 */
const youtube = google.youtube({
  version: 'v3',
  auth: process.env.YOUTUBE_API_KEY,
});

/**
 * Extract playlist ID from various YouTube URL formats
 * @param url - YouTube playlist URL
 * @returns Playlist ID or null if invalid
 */
export function extractPlaylistId(url: string): string | null {
  try {
    const urlObj = new URL(url);
    
    // Handle different YouTube URL formats
    if (urlObj.hostname.includes('youtube.com')) {
      return urlObj.searchParams.get('list');
    }
    
    if (urlObj.hostname.includes('youtu.be')) {
      // For youtu.be links with playlist parameter
      return urlObj.searchParams.get('list');
    }
    
    // Direct playlist ID
    if (url.match(/^[A-Za-z0-9_-]{34}$/)) {
      return url;
    }
    
    return null;
  } catch {
    return null;
  }
}

/**
 * Extract video ID from YouTube URL
 * @param url - YouTube video URL
 * @returns Video ID or null if invalid
 */
export function extractVideoId(url: string): string | null {
  try {
    const urlObj = new URL(url);
    
    if (urlObj.hostname.includes('youtube.com')) {
      return urlObj.searchParams.get('v');
    }
    
    if (urlObj.hostname.includes('youtu.be')) {
      return urlObj.pathname.slice(1);
    }
    
    // Direct video ID
    if (url.match(/^[A-Za-z0-9_-]{11}$/)) {
      return url;
    }
    
    return null;
  } catch {
    return null;
  }
}

/**
 * Validate YouTube video URL using ytdl-core
 * @param url - YouTube video URL
 * @returns Promise<boolean> - True if valid and accessible
 */
export async function validateVideoUrl(url: string): Promise<boolean> {
  try {
    return ytdl.validateURL(url);
  } catch {
    return false;
  }
}

/**
 * Get video information using ytdl-core
 * @param videoId - YouTube video ID
 * @returns Promise<ytdl.videoInfo | null> - Video info or null if error
 */
export async function getVideoInfo(videoId: string): Promise<ytdl.videoInfo | null> {
  try {
    const url = `https://www.youtube.com/watch?v=${videoId}`;
    return await ytdl.getInfo(url);
  } catch {
    return null;
  }
}

/**
 * Format duration from ISO 8601 to readable format
 * @param duration - ISO 8601 duration string (PT4M13S)
 * @returns Formatted duration string (4:13)
 */
export function formatDuration(duration: string): string {
  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
  if (!match) return '0:00';
  
  const hours = parseInt(match[1] || '0');
  const minutes = parseInt(match[2] || '0');
  const seconds = parseInt(match[3] || '0');
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }
  
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

/**
 * Fetch playlist information and videos from YouTube Data API
 * @param playlistId - YouTube playlist ID
 * @returns Promise<PlaylistInfo | null> - Playlist info or null if error
 */
export async function getPlaylistInfo(playlistId: string): Promise<PlaylistInfo | null> {
  try {
    // Get playlist details
    const playlistResponse = await youtube.playlists.list({
      part: ['snippet', 'contentDetails'],
      id: [playlistId],
    });

    if (!playlistResponse.data.items || playlistResponse.data.items.length === 0) {
      return null;
    }

    const playlist = playlistResponse.data.items[0];
    const videoCount = playlist.contentDetails?.itemCount || 0;

    // Get all videos in the playlist (handle pagination)
    const videos: VideoMetadata[] = [];
    let nextPageToken: string | undefined;

    do {
      const videosResponse = await youtube.playlistItems.list({
        part: ['snippet', 'contentDetails'],
        playlistId: playlistId,
        maxResults: 50,
        pageToken: nextPageToken,
      });

      if (videosResponse.data.items) {
        for (const item of videosResponse.data.items) {
          const snippet = item.snippet;
          if (snippet && snippet.resourceId?.videoId) {
            videos.push({
              id: snippet.resourceId.videoId,
              title: snippet.title || 'Unknown Title',
              duration: 'Unknown', // Will be filled by video details API
              thumbnail: snippet.thumbnails?.medium?.url || '',
              channelTitle: snippet.videoOwnerChannelTitle || snippet.channelTitle || 'Unknown Channel',
              publishedAt: snippet.publishedAt || '',
            });
          }
        }
      }

      nextPageToken = videosResponse.data.nextPageToken;
    } while (nextPageToken);

    // Get detailed video information for durations
    if (videos.length > 0) {
      const videoIds = videos.map(v => v.id);
      const chunks = [];
      
      // Process in chunks of 50 (API limit)
      for (let i = 0; i < videoIds.length; i += 50) {
        chunks.push(videoIds.slice(i, i + 50));
      }

      for (const chunk of chunks) {
        const videoDetailsResponse = await youtube.videos.list({
          part: ['contentDetails'],
          id: chunk,
        });

        if (videoDetailsResponse.data.items) {
          videoDetailsResponse.data.items.forEach((video, index) => {
            const videoIndex = videos.findIndex(v => v.id === video.id);
            if (videoIndex !== -1 && video.contentDetails?.duration) {
              videos[videoIndex].duration = formatDuration(video.contentDetails.duration);
            }
          });
        }
      }
    }

    return {
      id: playlistId,
      title: playlist.snippet?.title || 'Unknown Playlist',
      description: playlist.snippet?.description || '',
      videoCount,
      videos,
    };
  } catch (error) {
    console.error('Error fetching playlist info:', error);
    return null;
  }
}
