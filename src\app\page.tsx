'use client';

import { useState, useCallback } from 'react';
import { PlaylistForm } from '@/components/PlaylistForm';
import { VideoList } from '@/components/VideoList';
import { ProgressTracker, DownloadProgress } from '@/components/ProgressTracker';
import { ErrorDisplay } from '@/components/ErrorDisplay';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { VideoMetadata, PlaylistInfo } from '@/lib/youtube';
import { getErrorMessage, delay } from '@/lib/utils';

/**
 * Main page component for YouTube Playlist Downloader
 */
export default function Home() {
  // State management
  const [playlist, setPlaylist] = useState<PlaylistInfo | null>(null);
  const [downloads, setDownloads] = useState<DownloadProgress[]>([]);
  const [currentDownloadIndex, setCurrentDownloadIndex] = useState(0);
  const [isLoadingPlaylist, setIsLoadingPlaylist] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch playlist information from API
   */
  const fetchPlaylist = useCallback(async (url: string) => {
    setIsLoadingPlaylist(true);
    setError(null);
    setPlaylist(null);

    try {
      const response = await fetch(`/api/playlist?url=${encodeURIComponent(url)}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch playlist');
      }

      if (!data.success || !data.data) {
        throw new Error('Invalid response from server');
      }

      setPlaylist(data.data);

      // Initialize download progress for each video
      const initialDownloads: DownloadProgress[] = data.data.videos.map((video: VideoMetadata) => ({
        videoId: video.id,
        title: video.title,
        status: 'pending' as const,
      }));

      setDownloads(initialDownloads);
      setCurrentDownloadIndex(0);

    } catch (err) {
      const errorMessage = getErrorMessage(err);
      setError(errorMessage);
      console.error('Error fetching playlist:', err);
    } finally {
      setIsLoadingPlaylist(false);
    }
  }, []);

  /**
   * Download a single video
   */
  const downloadVideo = useCallback(async (videoId: string, index: number): Promise<boolean> => {
    try {
      // Update status to downloading
      setDownloads(prev => prev.map((download, i) =>
        i === index
          ? { ...download, status: 'downloading' }
          : download
      ));

      const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
      const response = await fetch(`/api/download?videoUrl=${encodeURIComponent(videoUrl)}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Download failed');
      }

      // Create download link
      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);

      // Extract filename from Content-Disposition header
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = `video-${videoId}.mp4`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch && filenameMatch[1]) {
          filename = decodeURIComponent(filenameMatch[1].replace(/['"]/g, ''));
        }
      }

      // Trigger download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      window.URL.revokeObjectURL(downloadUrl);

      // Update status to completed
      setDownloads(prev => prev.map((download, i) =>
        i === index
          ? { ...download, status: 'completed' }
          : download
      ));

      return true;

    } catch (err) {
      const errorMessage = getErrorMessage(err);

      // Update status to error
      setDownloads(prev => prev.map((download, i) =>
        i === index
          ? { ...download, status: 'error', error: errorMessage }
          : download
      ));

      console.error(`Error downloading video ${videoId}:`, err);
      return false;
    }
  }, []);

  /**
   * Download all videos in sequence
   */
  const downloadAllVideos = useCallback(async () => {
    if (!playlist || playlist.videos.length === 0) return;

    setIsDownloading(true);
    setCurrentDownloadIndex(0);

    try {
      for (let i = 0; i < playlist.videos.length; i++) {
        setCurrentDownloadIndex(i);
        const video = playlist.videos[i];

        await downloadVideo(video.id, i);

        // Add delay between downloads to avoid rate limiting
        if (i < playlist.videos.length - 1) {
          await delay(2000); // 2 second delay
        }
      }
    } catch (err) {
      console.error('Error during batch download:', err);
    } finally {
      setIsDownloading(false);
      setCurrentDownloadIndex(playlist.videos.length);
    }
  }, [playlist, downloadVideo]);

  /**
   * Reset the application state
   */
  const resetState = useCallback(() => {
    setPlaylist(null);
    setDownloads([]);
    setCurrentDownloadIndex(0);
    setIsDownloading(false);
    setError(null);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                YouTube Playlist Downloader
              </h1>
              <p className="mt-2 text-gray-600">
                Download all videos from any YouTube playlist
              </p>
            </div>

            {playlist && (
              <button
                onClick={resetState}
                disabled={isDownloading}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                New Playlist
              </button>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Error Display */}
          {error && (
            <ErrorDisplay
              title="Error"
              message={error}
              type="error"
              onDismiss={() => setError(null)}
            />
          )}

          {/* Playlist Form */}
          {!playlist && (
            <div className="flex justify-center">
              <PlaylistForm
                onSubmit={fetchPlaylist}
                isLoading={isLoadingPlaylist}
                disabled={isDownloading}
              />
            </div>
          )}

          {/* Loading State */}
          {isLoadingPlaylist && (
            <div className="flex justify-center py-12">
              <LoadingSpinner size="lg" text="Loading playlist..." />
            </div>
          )}

          {/* Playlist Content */}
          {playlist && (
            <div className="space-y-8">
              {/* Video List */}
              <div className="flex justify-center">
                <VideoList
                  videos={playlist.videos}
                  playlistTitle={playlist.title}
                  onDownloadAll={downloadAllVideos}
                  isDownloading={isDownloading}
                />
              </div>

              {/* Progress Tracker */}
              {downloads.length > 0 && (isDownloading || downloads.some(d => d.status !== 'pending')) && (
                <div className="flex justify-center">
                  <ProgressTracker
                    downloads={downloads}
                    currentIndex={currentDownloadIndex}
                    totalCount={playlist.videos.length}
                  />
                </div>
              )}
            </div>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-sm text-gray-500">
            <p>
              Built with Next.js, React, and YouTube Data API v3
            </p>
            <p className="mt-2">
              Please respect copyright and only download content you have permission to use.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
