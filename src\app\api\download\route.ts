import { NextRequest, NextResponse } from 'next/server';
import ytdl from 'ytdl-core';
import {
  extractVideoId,
  validateVideoUrl,
  getVideoInfo,
  getBestFormat,
  DownloadFormat,
  VideoQuality,
  AudioQuality
} from '@/lib/youtube';
import { sanitizeFilename, getErrorMessage } from '@/lib/utils';
import { convertToMp3, isValidQualityForFormat } from '@/lib/audio-converter';

/**
 * GET /api/download
 *
 * Downloads a YouTube video in specified format and quality
 *
 * Query Parameters:
 * - videoUrl: YouTube video URL or video ID
 * - format: Download format (mp4 or mp3, default: mp4)
 * - quality: Quality preference (video: 1080p, 720p, 480p, 360p, 240p, 144p; audio: 320kbps, 256kbps, 192kbps, 128kbps, 96kbps, 64kbps)
 *
 * Returns:
 * - 200: Video/Audio file stream
 * - 400: Invalid parameters
 * - 404: Video not found or unavailable
 * - 500: Server error
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const videoUrl = searchParams.get('videoUrl');
    const format = (searchParams.get('format') || 'mp4') as DownloadFormat;
    const quality = searchParams.get('quality') || (format === 'mp4' ? '720p' : '192kbps');

    // Validate input
    if (!videoUrl) {
      return NextResponse.json(
        {
          error: 'Missing required parameter: videoUrl',
          code: 'MISSING_VIDEO_URL'
        },
        { status: 400 }
      );
    }

    // Validate format
    if (!['mp4', 'mp3'].includes(format)) {
      return NextResponse.json(
        {
          error: 'Invalid format. Supported formats: mp4, mp3',
          code: 'INVALID_FORMAT'
        },
        { status: 400 }
      );
    }

    // Validate quality for format
    if (!isValidQualityForFormat(format, quality)) {
      return NextResponse.json(
        {
          error: `Invalid quality "${quality}" for format "${format}"`,
          code: 'INVALID_QUALITY'
        },
        { status: 400 }
      );
    }

    // Extract video ID and construct full URL
    const videoId = extractVideoId(videoUrl);
    if (!videoId) {
      return NextResponse.json(
        { 
          error: 'Invalid YouTube video URL. Please provide a valid video URL.',
          code: 'INVALID_VIDEO_URL'
        },
        { status: 400 }
      );
    }

    const fullVideoUrl = `https://www.youtube.com/watch?v=${videoId}`;

    // Validate video URL
    const isValid = await validateVideoUrl(fullVideoUrl);
    if (!isValid) {
      return NextResponse.json(
        { 
          error: 'Video not found or unavailable. It may be private, deleted, or region-restricted.',
          code: 'VIDEO_UNAVAILABLE'
        },
        { status: 404 }
      );
    }

    // Get video information for filename and format selection
    const videoInfo = await getVideoInfo(videoId);
    if (!videoInfo) {
      return NextResponse.json(
        {
          error: 'Unable to fetch video information.',
          code: 'VIDEO_INFO_ERROR'
        },
        { status: 500 }
      );
    }

    // Get the best format for the requested quality and format
    let bestFormat = getBestFormat(videoInfo, format, quality as VideoQuality | AudioQuality);
    let actualQuality = quality;

    const videoTitle = videoInfo.videoDetails.title;
    const sanitizedTitle = sanitizeFilename(videoTitle);

    // If no exact format found, try fallback qualities
    if (!bestFormat && format === 'mp4') {
      const fallbackQualities: VideoQuality[] = ['720p', '480p', '360p', '240p', '144p'];
      for (const fallbackQuality of fallbackQualities) {
        bestFormat = getBestFormat(videoInfo, format, fallbackQuality);
        if (bestFormat) {
          actualQuality = fallbackQuality;
          console.log(`Falling back to ${fallbackQuality} for video ${videoId}`);
          break;
        }
      }
    } else if (!bestFormat && format === 'mp3') {
      const fallbackQualities: AudioQuality[] = ['192kbps', '128kbps', '96kbps', '64kbps'];
      for (const fallbackQuality of fallbackQualities) {
        bestFormat = getBestFormat(videoInfo, format, fallbackQuality);
        if (bestFormat) {
          actualQuality = fallbackQuality;
          console.log(`Falling back to ${fallbackQuality} for video ${videoId}`);
          break;
        }
      }
    }

    if (!bestFormat) {
      return NextResponse.json(
        {
          error: `No suitable format found for ${format}. This video may not support the requested format.`,
          code: 'FORMAT_NOT_AVAILABLE',
          availableFormats: videoInfo.formats.map(f => ({
            quality: f.height || 'audio',
            hasVideo: f.hasVideo,
            hasAudio: f.hasAudio,
            container: f.container
          }))
        },
        { status: 404 }
      );
    }

    // Create filename with actual quality used
    const fileExtension = format === 'mp3' ? 'mp3' : 'mp4';
    const filename = `${sanitizedTitle}_${actualQuality}.${fileExtension}`;

    // Set up download options based on the best format
    const downloadOptions: ytdl.downloadOptions = {
      format: bestFormat,
    };

    // Create download stream
    const videoStream = ytdl(fullVideoUrl, downloadOptions);

    // Set up response headers for file download
    const contentType = format === 'mp3' ? 'audio/mpeg' : 'video/mp4';
    const headers = new Headers({
      'Content-Type': contentType,
      'Content-Disposition': `attachment; filename="${encodeURIComponent(filename)}"`,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
    });

    // Handle MP3 conversion if needed
    if (format === 'mp3') {
      try {
        const mp3Stream = await convertToMp3(videoStream, quality as AudioQuality);

        // Create a ReadableStream from the converted MP3 stream
        const readableStream = new ReadableStream({
          start(controller) {
            mp3Stream.on('data', (chunk) => {
              controller.enqueue(chunk);
            });

            mp3Stream.on('end', () => {
              controller.close();
            });

            mp3Stream.on('error', (error) => {
              console.error('MP3 conversion error:', error);
              controller.error(error);
            });
          },
          cancel() {
            mp3Stream.destroy();
          }
        });

        return new NextResponse(readableStream, {
          status: 200,
          headers,
        });
      } catch (error) {
        console.error('MP3 conversion failed:', error);
        return NextResponse.json(
          {
            error: 'MP3 conversion failed. Please try a different quality or format.',
            code: 'CONVERSION_ERROR'
          },
          { status: 500 }
        );
      }
    }

    // Handle MP4 download
    videoStream.on('error', (error) => {
      console.error('Video stream error:', error);
    });

    // Create a ReadableStream from the ytdl stream
    const readableStream = new ReadableStream({
      start(controller) {
        videoStream.on('data', (chunk) => {
          controller.enqueue(chunk);
        });

        videoStream.on('end', () => {
          controller.close();
        });

        videoStream.on('error', (error) => {
          console.error('Stream error:', error);
          controller.error(error);
        });
      },
      cancel() {
        videoStream.destroy();
      }
    });

    return new NextResponse(readableStream, {
      status: 200,
      headers,
    });

  } catch (error) {
    console.error('Download API error:', error);
    
    const errorMessage = getErrorMessage(error);
    
    // Handle specific ytdl errors
    if (errorMessage.includes('Video unavailable')) {
      return NextResponse.json(
        { 
          error: 'Video is unavailable. It may be private, deleted, or region-restricted.',
          code: 'VIDEO_UNAVAILABLE'
        },
        { status: 404 }
      );
    }
    
    if (errorMessage.includes('age-restricted')) {
      return NextResponse.json(
        { 
          error: 'Video is age-restricted and cannot be downloaded.',
          code: 'AGE_RESTRICTED'
        },
        { status: 403 }
      );
    }
    
    if (errorMessage.includes('private')) {
      return NextResponse.json(
        { 
          error: 'Video is private and cannot be downloaded.',
          code: 'PRIVATE_VIDEO'
        },
        { status: 403 }
      );
    }
    
    // Generic server error
    return NextResponse.json(
      { 
        error: 'An unexpected error occurred while downloading the video.',
        code: 'DOWNLOAD_ERROR',
        details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/download
 *
 * Alternative endpoint for video download with request body
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { videoUrl, format, quality } = body;

    if (!videoUrl) {
      return NextResponse.json(
        {
          error: 'Missing required field: videoUrl',
          code: 'MISSING_VIDEO_URL'
        },
        { status: 400 }
      );
    }

    // Redirect to GET method by creating a new URL with query parameters
    const searchParams = new URLSearchParams({
      videoUrl,
      ...(format && { format }),
      ...(quality && { quality })
    });

    const getRequest = new NextRequest(
      `${request.nextUrl.origin}/api/download?${searchParams.toString()}`,
      { method: 'GET' }
    );

    return GET(getRequest);

  } catch (error) {
    console.error('Download POST API error:', error);

    return NextResponse.json(
      {
        error: 'Invalid request body. Expected JSON with videoUrl field.',
        code: 'INVALID_BODY'
      },
      { status: 400 }
    );
  }
}
