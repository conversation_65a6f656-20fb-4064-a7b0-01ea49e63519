import { NextRequest, NextResponse } from 'next/server';
import ytdl from 'ytdl-core';
import { extractVideoId, validateVideoUrl, getVideoInfo } from '@/lib/youtube';
import { downloadRateLimiter, sanitizeFilename, getErrorMessage } from '@/lib/utils';

/**
 * GET /api/download
 * 
 * Downloads a YouTube video as MP4 file
 * 
 * Query Parameters:
 * - videoUrl: YouTube video URL or video ID
 * - quality: Video quality preference (optional: 720p, 480p, 360p)
 * 
 * Returns:
 * - 200: Video file stream
 * - 400: Invalid video URL or missing parameters
 * - 403: Rate limit exceeded
 * - 404: Video not found or unavailable
 * - 500: Server error
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const videoUrl = searchParams.get('videoUrl');
    const quality = searchParams.get('quality') || '720p';

    // Validate input
    if (!videoUrl) {
      return NextResponse.json(
        { 
          error: 'Missing required parameter: videoUrl',
          code: 'MISSING_VIDEO_URL'
        },
        { status: 400 }
      );
    }

    // Check rate limiting
    if (!downloadRateLimiter.isAllowed()) {
      const timeUntilReset = downloadRateLimiter.getTimeUntilReset();
      return NextResponse.json(
        { 
          error: 'Rate limit exceeded. Please try again later.',
          code: 'RATE_LIMIT_EXCEEDED',
          retryAfter: Math.ceil(timeUntilReset / 1000)
        },
        { 
          status: 429,
          headers: {
            'Retry-After': Math.ceil(timeUntilReset / 1000).toString()
          }
        }
      );
    }

    // Extract video ID and construct full URL
    const videoId = extractVideoId(videoUrl);
    if (!videoId) {
      return NextResponse.json(
        { 
          error: 'Invalid YouTube video URL. Please provide a valid video URL.',
          code: 'INVALID_VIDEO_URL'
        },
        { status: 400 }
      );
    }

    const fullVideoUrl = `https://www.youtube.com/watch?v=${videoId}`;

    // Validate video URL
    const isValid = await validateVideoUrl(fullVideoUrl);
    if (!isValid) {
      return NextResponse.json(
        { 
          error: 'Video not found or unavailable. It may be private, deleted, or region-restricted.',
          code: 'VIDEO_UNAVAILABLE'
        },
        { status: 404 }
      );
    }

    // Get video information for filename
    const videoInfo = await getVideoInfo(videoId);
    if (!videoInfo) {
      return NextResponse.json(
        { 
          error: 'Unable to fetch video information.',
          code: 'VIDEO_INFO_ERROR'
        },
        { status: 500 }
      );
    }

    const videoTitle = videoInfo.videoDetails.title;
    const sanitizedTitle = sanitizeFilename(videoTitle);
    const filename = `${sanitizedTitle}.mp4`;

    // Set up video download options
    const downloadOptions: ytdl.downloadOptions = {
      quality: 'highestvideo',
      filter: (format) => {
        // Prefer MP4 format with both video and audio
        if (format.container === 'mp4' && format.hasVideo && format.hasAudio) {
          return true;
        }
        return false;
      },
    };

    // If no combined format available, try video-only with specified quality
    try {
      const formats = ytdl.filterFormats(videoInfo.formats, downloadOptions.filter!);
      if (formats.length === 0) {
        // Fallback to best available format
        downloadOptions.quality = 'highest';
        delete downloadOptions.filter;
      }
    } catch (error) {
      console.warn('Format filtering failed, using highest quality:', error);
      downloadOptions.quality = 'highest';
      delete downloadOptions.filter;
    }

    // Create download stream
    const videoStream = ytdl(fullVideoUrl, downloadOptions);

    // Set up response headers for file download
    const headers = new Headers({
      'Content-Type': 'video/mp4',
      'Content-Disposition': `attachment; filename="${encodeURIComponent(filename)}"`,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
    });

    // Handle stream errors
    videoStream.on('error', (error) => {
      console.error('Video stream error:', error);
    });

    // Create a ReadableStream from the ytdl stream
    const readableStream = new ReadableStream({
      start(controller) {
        videoStream.on('data', (chunk) => {
          controller.enqueue(chunk);
        });

        videoStream.on('end', () => {
          controller.close();
        });

        videoStream.on('error', (error) => {
          console.error('Stream error:', error);
          controller.error(error);
        });
      },
      cancel() {
        videoStream.destroy();
      }
    });

    return new NextResponse(readableStream, {
      status: 200,
      headers,
    });

  } catch (error) {
    console.error('Download API error:', error);
    
    const errorMessage = getErrorMessage(error);
    
    // Handle specific ytdl errors
    if (errorMessage.includes('Video unavailable')) {
      return NextResponse.json(
        { 
          error: 'Video is unavailable. It may be private, deleted, or region-restricted.',
          code: 'VIDEO_UNAVAILABLE'
        },
        { status: 404 }
      );
    }
    
    if (errorMessage.includes('age-restricted')) {
      return NextResponse.json(
        { 
          error: 'Video is age-restricted and cannot be downloaded.',
          code: 'AGE_RESTRICTED'
        },
        { status: 403 }
      );
    }
    
    if (errorMessage.includes('private')) {
      return NextResponse.json(
        { 
          error: 'Video is private and cannot be downloaded.',
          code: 'PRIVATE_VIDEO'
        },
        { status: 403 }
      );
    }
    
    // Generic server error
    return NextResponse.json(
      { 
        error: 'An unexpected error occurred while downloading the video.',
        code: 'DOWNLOAD_ERROR',
        details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/download
 * 
 * Alternative endpoint for video download with request body
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { videoUrl, quality } = body;

    if (!videoUrl) {
      return NextResponse.json(
        { 
          error: 'Missing required field: videoUrl',
          code: 'MISSING_VIDEO_URL'
        },
        { status: 400 }
      );
    }

    // Redirect to GET method by creating a new URL with query parameters
    const searchParams = new URLSearchParams({ 
      videoUrl,
      ...(quality && { quality })
    });
    
    const getRequest = new NextRequest(
      `${request.nextUrl.origin}/api/download?${searchParams.toString()}`,
      { method: 'GET' }
    );

    return GET(getRequest);

  } catch (error) {
    console.error('Download POST API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Invalid request body. Expected JSON with videoUrl field.',
        code: 'INVALID_BODY'
      },
      { status: 400 }
    );
  }
}
